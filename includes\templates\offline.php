<?php
/**
 * Template for the offline page
 */
?>
<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><?php _e('Offline - No Internet Connection', 'q-pusher-q-pwa'); ?></title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
            line-height: 1.6;
        }
    </style>
    <link rel="stylesheet" href="<?php echo esc_url(plugin_dir_url(dirname(dirname(__FILE__))) . 'includes/css/offline.css'); ?>" media="all">
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.42 0-8-3.58-8-8 0-1.85.63-3.55 1.69-4.9L16.9 18.31C15.55 19.37 13.85 20 12 20zm6.31-3.1L7.1 5.69C8.45 4.63 10.15 4 12 4c4.42 0 8 3.58 8 8 0 1.85-.63 3.55-1.69 4.9z"/>
            </svg>
        </div>
        <h1><?php _e('You\'re offline', 'q-pusher-q-pwa'); ?></h1>
        <p><?php _e('It looks like you\'re not connected to the internet. Check your connection and try again.', 'q-pusher-q-pwa'); ?></p>
        <button class="retry-button" onclick="window.location.reload()">
            <?php _e('Retry', 'q-pusher-q-pwa'); ?>
        </button>
        
        <div class="cached-content" id="cached-pages" style="display: none;">
            <h2><?php _e('Available offline pages', 'q-pusher-q-pwa'); ?></h2>
            <div class="cached-links" id="cached-links">
                <!-- Cached links will be populated here by JavaScript -->
            </div>
        </div>
    </div>

   
</body>
</html>