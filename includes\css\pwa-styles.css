/**
 * PWA Styles - Progressive Web App specific styles
 */

/* PWA Install Prompt */
.q-pwa-install-container {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  /* transition: transform 0.3s ease, opacity 0.3s ease; */
  background-color: #f4f5f7;
  display: flex;
  align-items: center;
  justify-content: center;
}

.q-pwa-install-container.hidden {
  transform: translateY(100%);
  opacity: 0;
  pointer-events: none;
}

/* iOS Install Instructions */
.q-pwa-ios-instructions {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 10000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.q-pwa-ios-instructions-content {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  width: 90%;
  max-width: 350px;
  position: relative;
}

.q-pwa-ios-close {
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.q-pwa-ios-instructions-content h3 {
  margin-top: 0;
  margin-bottom: 15px;
  text-align: center;
}

.q-pwa-ios-instructions-content ol {
  margin: 0;
  padding-left: 20px;
}

.q-pwa-ios-instructions-content li {
  margin-bottom: 10px;
}

.ios-share-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  border: 1px solid #ccc;
  border-radius: 4px;
  margin-left: 5px;
}

.q-pwa-install-prompt {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
}

.q-pwa-install-content {
  background: #ffffff;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.q-pwa-install-icon {
  font-size: 48px;
  flex-shrink: 0;
}

.q-pwa-install-text {
  flex: 1;
}

.q-pwa-install-text h3 {
  margin: 0 0 5px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.q-pwa-install-text p {
  margin: 0;
  color: #666;
  font-size: 14px;
  line-height: 1.4;
}

.q-pwa-install-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex-shrink: 0;
}

.q-pwa-btn-primary {
  font-size: 13px;
  display: inline-block;
  border: 1px solid #d0d5dd;
  padding: 8px 15px;
  border-radius: 8px;
  box-shadow: 0px 0px 1px rgba(16, 24, 40, 0.18) inset,
    0px 1px 2px 0px rgba(16, 24, 40, 0.05) !important;
  transition: all 0.2s ease;
  background: #fff;
  color: #344054;
  font-weight: 500;
  cursor: pointer;
}

.q-pwa-btn-primary:hover {
  background: #005a87;
}

.q-pwa-btn-secondary {
  background: transparent;
  color: #000;
  border: 1px solid #000;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.q-pwa-btn-secondary:hover {
  background: #000;
  color: #fff;
}

/* PWA App Shell */
.pwa-enabled {
  /* Base styles for PWA-enabled sites */
}

.pwa-installed {
  /* Styles when app is installed */
}

.pwa-installed .site-header {
  /* Adjust header for standalone mode */
  padding-top: env(safe-area-inset-top);
}

.pwa-installed .site-footer {
  /* Adjust footer for standalone mode */
  padding-bottom: env(safe-area-inset-bottom);
}

/* iOS Standalone Mode */
.ios-standalone {
  /* iOS-specific standalone styles */
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
  height: 100%;
  width: 100%;
  position: fixed;
}

.ios-standalone .site-header {
  padding-top: 44px; /* Status bar height */
}

/* Fix for iOS blank screen issue */
@supports (-webkit-touch-callout: none) {
  html,
  body {
    height: 100%;
    overflow: auto;
    -webkit-overflow-scrolling: touch;
  }

  body {
    /* Ensure body is visible on iOS */
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
  }
}

/* iOS PWA Immediate Splash Screen */
#ios-pwa-immediate-splash {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 999999;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.3s ease-out;
  -webkit-user-select: none;
  user-select: none;
  background-color: var(--pwa-background-color, #ffffff);
}

#ios-pwa-immediate-splash img,
#ios-pwa-immediate-splash div {
  max-width: 120px;
  max-height: 120px;
  width: 25vw;
  height: 25vw;
  object-fit: contain;
  border-radius: 22.5%;
  animation: iosPulse 1.5s infinite ease-in-out;
}

@keyframes iosPulse {
  0% {
    transform: scale(0.95);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.05);
    opacity: 1;
  }
  100% {
    transform: scale(0.95);
    opacity: 0.8;
  }
}

/* Responsive Design for PWA */
@media (display-mode: standalone) {
  /* Styles when running as installed PWA */
  body {
    user-select: none; /* Prevent text selection in app mode */
  }

  /* Hide browser-specific elements */
  .browser-only {
    display: none !important;
  }

  /* Show PWA-specific elements */
  .pwa-only {
    display: block !important;
  }
}

@media (display-mode: fullscreen) {
  /* Styles for fullscreen mode */
  .site-header,
  .site-footer {
    display: none;
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .q-pwa-install-content {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .q-pwa-install-actions {
    flex-direction: row;
    justify-content: center;
    width: 100%;
  }
}

@media (max-width: 480px) {
  .q-pwa-install-prompt {
    padding: 15px;
  }

  .q-pwa-install-content {
    padding: 15px;
  }

  .q-pwa-install-text h3 {
    font-size: 16px;
  }

  .q-pwa-install-text p {
    font-size: 13px;
  }

  .q-pwa-btn-primary {
    padding: 10px 20px;
    font-size: 14px;
  }
}

/* Animations */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes progressBar {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

/* Contact Picker Styles */
.q-pwa-contact-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.q-pwa-contact-container.hidden {
  transform: translateY(100%);
  opacity: 0;
  pointer-events: none;
}

.q-pwa-contact-prompt {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
}

.q-pwa-contact-content {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.q-pwa-contact-icon {
  font-size: 48px;
  flex-shrink: 0;
}

.q-pwa-contact-text h3 {
  margin: 0 0 5px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.q-pwa-contact-text p {
  margin: 0;
  color: #666;
  font-size: 14px;
  line-height: 1.4;
}

.q-pwa-contact-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex-shrink: 0;
}

@keyframes loading {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .q-pwa-install-icon {
    /* High-resolution icon styles */
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .q-pwa-contact-content {
    background: #2c2c2c;
    color: #fff;
  }

  .q-pwa-install-text h3 {
    color: #fff;
  }

  .q-pwa-install-text p {
    color: #ccc;
  }
}

/* Subscription Toggle Styles */
#q-subscription-toggle {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  min-width: 120px;
  justify-content: center;
}

#q-subscription-toggle i {
  transition: all 0.3s ease;
  font-size: 1.1em;
}

#q-subscription-toggle span {
  transition: all 0.3s ease;
}

/* Loading state */
#q-subscription-toggle.loading {
  pointer-events: none;
}

#q-subscription-toggle.loading i {
  animation: spin 1s linear infinite;
}

/* Subscribed state */
#q-subscription-toggle.subscribed {
  background-color: #dc3545;
  border-color: #dc3545;
}

#q-subscription-toggle.subscribed:hover {
  background-color: #c82333;
  border-color: #bd2130;
  transform: translateY(-1px);
}

/* Unsubscribed state hover */
#q-subscription-toggle:not(.subscribed):not(.loading):hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Focus styles for accessibility */
#q-subscription-toggle:focus {
  outline: 2px solid #007cba;
  outline-offset: 2px;
}

/* Disabled state */
#q-subscription-toggle:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* Ripple effect */
#q-subscription-toggle::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
}

#q-subscription-toggle:active::before {
  width: 100px;
  height: 100px;
}

/* Notification styles */
.q-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  max-width: 400px;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  animation: slideInRight 0.3s ease;
  cursor: pointer;
}

.q-notification-success {
  background: #d4edda;
  border: 1px solid #c3e6cb;
  color: #155724;
}

.q-notification-error {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
}

.q-notification-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.q-notification-content i {
  font-size: 1.2em;
  flex-shrink: 0;
}

/* Animations */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .q-notification {
    left: 20px;
    right: 20px;
    max-width: none;
  }

  #q-subscription-toggle {
    min-width: 100px;
    font-size: 0.9em;
  }
}

/* Print Styles */
@media print {
  .q-pwa-contact-container,
  .q-pwa-install-container,
  .q-notification,
  #q-subscription-toggle {
    display: none !important;
  }
}
