<?php
if (!defined('ABSPATH')) {
    exit;
}

class Q_PWA_Settings
{
    public static function init()
    {
        add_action('admin_menu', [self::class, 'add_pwa_settings_page']);
        add_action('admin_init', [self::class, 'register_pwa_settings']);
        add_action('wp_enqueue_scripts', [self::class, 'enqueue_pwa_scripts']);
        add_action('wp_head', [self::class, 'add_pwa_meta_tags']);
        add_action('wp_head', [self::class, 'add_manifest_link']);
        add_action('admin_enqueue_scripts', [self::class, 'enqueue_pwa_scripts']);

        // Flush manifest cache when any PWA setting is updated
        add_action('updated_option', [self::class, 'maybe_flush_manifest_cache'], 10, 3);

        // Remove default WordPress site icon when PWA icon is provided so it doesn't override the PWA icon
        add_action('wp_head', [self::class, 'remove_site_icon_meta'], 1);
    }

    public static function add_pwa_settings_page()
    {
        add_submenu_page(
            'options-general.php',
            'PWA Settings',
            'P<PERSON> Settings',
            'manage_options',
            'q-pwa-settings',
            [self::class, 'render_pwa_settings_page']
        );
    }

    public static function register_pwa_settings()
    {
        // PWA Basic Settings
        register_setting('q_pwa_settings', 'q_pwa_enabled', [
            'type' => 'boolean',
            'default' => false,
            'sanitize_callback' => 'rest_sanitize_boolean'
        ]);

        // Analytics Settings
        register_setting('q_pwa_settings', 'q_pwa_analytics_enabled', [
            'type' => 'boolean',
            'default' => true,
            'sanitize_callback' => 'rest_sanitize_boolean'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_analytics_retention', [
            'type' => 'integer',
            'default' => 90, // days
            'sanitize_callback' => 'absint'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_analytics_refresh_interval', [
            'type' => 'integer',
            'default' => 30, // seconds
            'sanitize_callback' => 'absint'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_app_name', [
            'type' => 'string',
            'default' => get_bloginfo('name'),
            'sanitize_callback' => 'sanitize_text_field'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_app_short_name', [
            'type' => 'string',
            'default' => get_bloginfo('name'),
            'sanitize_callback' => 'sanitize_text_field'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_app_description', [
            'type' => 'string',
            'default' => get_bloginfo('description'),
            'sanitize_callback' => 'sanitize_textarea_field'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_theme_color', [
            'type' => 'string',
            'default' => '#ffffff',
            'sanitize_callback' => 'sanitize_hex_color'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_background_color', [
            'type' => 'string',
            'default' => '#ffffff',
            'sanitize_callback' => 'sanitize_hex_color'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_splash_theme_color', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'sanitize_hex_color'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_splash_duration', [
            'type' => 'integer',
            'default' => 2000,
            'sanitize_callback' => 'absint'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_display_mode', [
            'type' => 'string',
            'default' => 'standalone',
            'sanitize_callback' => [self::class, 'sanitize_display_mode']
        ]);

        register_setting('q_pwa_settings', 'q_pwa_orientation', [
            'type' => 'string',
            'default' => 'any',
            'sanitize_callback' => [self::class, 'sanitize_orientation']
        ]);

        register_setting('q_pwa_settings', 'q_pwa_start_url', [
            'type' => 'string',
            'default' => '/',
            'sanitize_callback' => 'esc_url_raw'
        ]);

        // Simplified Icon Settings
        register_setting('q_pwa_settings', 'q_pwa_icon', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'esc_url_raw'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_splash', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'esc_url_raw'
        ]);

        // PWA Screenshots Settings
        register_setting('q_pwa_settings', 'q_pwa_screenshot_wide', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'esc_url_raw'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_screenshot_narrow', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'esc_url_raw'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_ios_status_bar_style', [
            'type' => 'string',
            'default' => 'default',
            'sanitize_callback' => [self::class, 'sanitize_ios_status_bar_style']
        ]);

        register_setting('q_pwa_settings', 'q_pwa_ios_web_app_capable', [
            'type' => 'boolean',
            'default' => true,
            'sanitize_callback' => 'rest_sanitize_boolean'
        ]);


        // Android-specific Settings
        register_setting('q_pwa_settings', 'q_pwa_maskable_icon', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'esc_url_raw'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_nav_color', [
            'type' => 'string',
            'default' => '#ffffff',
            'sanitize_callback' => 'sanitize_hex_color'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_monochrome_icon', [
            'type' => 'boolean',
            'default' => false,
            'sanitize_callback' => 'rest_sanitize_boolean'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_monochrome_icon_url', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'esc_url_raw'
        ]);

        // App Shortcuts Settings
        register_setting('q_pwa_settings', 'q_pwa_shortcuts_enabled', [
            'type' => 'boolean',
            'default' => false,
            'sanitize_callback' => 'rest_sanitize_boolean'
        ]);

        // Register shortcut settings (up to 4 shortcuts)
        for ($i = 1; $i <= 4; $i++) {
            register_setting('q_pwa_settings', "q_pwa_shortcut_{$i}_name", [
                'type' => 'string',
                'default' => '',
                'sanitize_callback' => 'sanitize_text_field'
            ]);

            register_setting('q_pwa_settings', "q_pwa_shortcut_{$i}_url", [
                'type' => 'string',
                'default' => '',
                'sanitize_callback' => 'esc_url_raw'
            ]);

            register_setting('q_pwa_settings', "q_pwa_shortcut_{$i}_description", [
                'type' => 'string',
                'default' => '',
                'sanitize_callback' => 'sanitize_text_field'
            ]);

            register_setting('q_pwa_settings', "q_pwa_shortcut_{$i}_icon", [
                'type' => 'string',
                'default' => '',
                'sanitize_callback' => 'esc_url_raw'
            ]);
        }

        // Desktop-specific Settings
        register_setting('q_pwa_settings', 'q_pwa_window_controls_overlay', [
            'type' => 'boolean',
            'default' => false,
            'sanitize_callback' => 'rest_sanitize_boolean'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_wco_caption_button_close', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'sanitize_text_field'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_protocol_handlers_enabled', [
            'type' => 'boolean',
            'default' => false,
            'sanitize_callback' => 'rest_sanitize_boolean'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_protocol_list', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'sanitize_text_field'
        ]);

        // Advanced Manifest Features
        register_setting('q_pwa_settings', 'q_pwa_categories', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'sanitize_text_field'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_scope', [
            'type' => 'string',
            'default' => '/',
            'sanitize_callback' => 'esc_url_raw'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_scope_extensions', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'sanitize_text_field'
        ]);


        register_setting('q_pwa_settings', 'q_pwa_android_package', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'sanitize_text_field'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_ios_app_id', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'sanitize_text_field'
        ]);

        // Related Applications Settings
        register_setting('q_pwa_settings', 'q_pwa_related_apps_enabled', [
            'type' => 'boolean',
            'default' => false,
            'sanitize_callback' => 'rest_sanitize_boolean'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_prefer_related_apps', [
            'type' => 'boolean',
            'default' => false,
            'sanitize_callback' => 'rest_sanitize_boolean'
        ]);

        // Register up to 5 related applications
        for ($i = 1; $i <= 5; $i++) {
            register_setting('q_pwa_settings', "q_pwa_related_app_{$i}_platform", [
                'type' => 'string',
                'default' => '',
                'sanitize_callback' => 'sanitize_text_field'
            ]);

            register_setting('q_pwa_settings', "q_pwa_related_app_{$i}_url", [
                'type' => 'string',
                'default' => '',
                'sanitize_callback' => 'esc_url_raw'
            ]);

            register_setting('q_pwa_settings', "q_pwa_related_app_{$i}_id", [
                'type' => 'string',
                'default' => '',
                'sanitize_callback' => 'sanitize_text_field'
            ]);
        }









        // Display Override Settings
        register_setting('q_pwa_settings', 'q_pwa_display_override', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'sanitize_text_field'
        ]);

        // Additional Icon Settings (192x192 and 512x512) & Apple Touch Icon
        // These options are used in the settings UI but were not previously registered, so they were not being saved.
        register_setting('q_pwa_settings', 'q_pwa_icon_192', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'esc_url_raw'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_icon_512', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'esc_url_raw'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_apple_touch_icon', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'esc_url_raw'
        ]);

        // Offline Fallback Page Setting
        register_setting('q_pwa_settings', 'q_pwa_offline_page_id', [
            'type' => 'integer',
            'default' => 0,
            'sanitize_callback' => 'absint'
        ]);

        // Caching Strategy Setting
        register_setting('q_pwa_settings', 'q_pwa_caching_strategy', [
            'type' => 'string',
            'default' => 'network-first',
            'sanitize_callback' => function ($value) {
                $allowed = ['cache-first', 'network-first', 'stale-while-revalidate'];
                return in_array($value, $allowed) ? $value : 'network-first';
            }
        ]);

        // Cache Version Setting
        register_setting('q_pwa_settings', 'q_pwa_cache_version', [
            'type' => 'string',
            'default' => 'v2',
            'sanitize_callback' => 'sanitize_text_field'
        ]);

        // Desktop-specific Settings
        register_setting('q_pwa_settings', 'q_pwa_installable_on_desktop', [
            'type' => 'boolean',
            'default' => false,
            'sanitize_callback' => 'rest_sanitize_boolean'
        ]);

        // Periodic Background Sync Settings
        register_setting('q_pwa_settings', 'q_pwa_sync_enabled', [
            'type' => 'boolean',
            'default' => true,
            'sanitize_callback' => 'rest_sanitize_boolean'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_sync_interval', [
            'type' => 'integer',
            'default' => 3600000, // 1 hour in milliseconds
            'sanitize_callback' => 'absint'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_sync_auto_reload', [
            'type' => 'boolean',
            'default' => false,
            'sanitize_callback' => 'rest_sanitize_boolean'
        ]);

        // Add settings sections
        add_settings_section(
            'q_pwa_basic_section',
            'Basic PWA Settings',
            [self::class, 'render_basic_section_description'],
            'q_pwa_settings'
        );

        add_settings_section(
            'q_pwa_appearance_section',
            'Appearance Settings',
            [self::class, 'render_appearance_section_description'],
            'q_pwa_settings'
        );

        add_settings_section(
            'q_pwa_icons_section',
            'App Icons',
            [self::class, 'render_icons_section_description'],
            'q_pwa_settings'
        );

        add_settings_section(
            'q_pwa_advanced_section',
            'Advanced Features',
            [self::class, 'render_advanced_section_description'],
            'q_pwa_settings'
        );

        add_settings_section(
            'q_pwa_sync_section',
            'Background Sync Settings',
            [self::class, 'render_sync_section_description'],
            'q_pwa_settings'
        );

        // Add background sync settings fields
        add_settings_field(
            'q_pwa_sync_enabled',
            'Enable Background Sync',
            [self::class, 'render_checkbox_field'],
            'q_pwa_settings',
            'q_pwa_sync_section',
            ['field' => 'q_pwa_sync_enabled', 'description' => 'Keep your app updated in the background (requires browser support)']
        );

        add_settings_field(
            'q_pwa_sync_interval',
            'Sync Interval',
            [self::class, 'render_select_field'],
            'q_pwa_settings',
            'q_pwa_sync_section',
            [
                'field' => 'q_pwa_sync_interval',
                'options' => [
                    '900000' => '15 Minutes',
                    '1800000' => '30 Minutes',
                    '3600000' => '1 Hour (Recommended)',
                    '7200000' => '2 Hours',
                    '14400000' => '4 Hours',
                    '28800000' => '8 Hours',
                    '86400000' => '24 Hours'
                ],
                'description' => 'How often the app should sync content in the background (minimum interval, actual timing may vary)'
            ]
        );

        add_settings_field(
            'q_pwa_sync_auto_reload',
            'Auto Reload Content',
            [self::class, 'render_checkbox_field'],
            'q_pwa_settings',
            'q_pwa_sync_section',
            ['field' => 'q_pwa_sync_auto_reload', 'description' => 'Automatically reload the page when new content is available (only when user is inactive)']
        );

        add_settings_section(
            'q_pwa_analytics_section',
            'Analytics Settings',
            [self::class, 'render_analytics_section_description'],
            'q_pwa_settings'
        );

        // Add analytics settings fields
        add_settings_field(
            'q_pwa_analytics_enabled',
            'Enable Analytics',
            [self::class, 'render_checkbox_field'],
            'q_pwa_settings',
            'q_pwa_analytics_section',
            ['field' => 'q_pwa_analytics_enabled', 'description' => 'Collect usage data for your PWA']
        );

        add_settings_field(
            'q_pwa_analytics_retention',
            'Data Retention Period',
            [self::class, 'render_select_field'],
            'q_pwa_settings',
            'q_pwa_analytics_section',
            [
                'field' => 'q_pwa_analytics_retention',
                'options' => [
                    '30' => '30 Days',
                    '60' => '60 Days',
                    '90' => '90 Days',
                    '180' => '180 Days',
                    '365' => '1 Year'
                ],
                'description' => 'How long to keep analytics data before automatic deletion'
            ]
        );

        // Add settings fields
        self::add_settings_fields();

        // Add Windows Control Overlay settings fields
        self::add_wco_settings_fields();

        // Add offline fallback page field
        add_settings_field(
            'q_pwa_offline_page_id',
            'Offline Fallback Page',
            [self::class, 'render_page_dropdown_field'],
            'q_pwa_settings',
            'q_pwa_basic_section',
            [
                'field' => 'q_pwa_offline_page_id',
                'description' => 'Select the WordPress page to show when offline.'
            ]
        );

        // Add caching strategy field
        add_settings_field(
            'q_pwa_caching_strategy',
            'Caching Strategy',
            [self::class, 'render_select_field'],
            'q_pwa_settings',
            'q_pwa_basic_section',
            [
                'field' => 'q_pwa_caching_strategy',
                'options' => [
                    'cache-first' => 'Cache First',
                    'network-first' => 'Network First',
                    'stale-while-revalidate' => 'Stale While Revalidate'
                ],
                'description' => 'Choose how the service worker should cache resources.'
            ]
        );

        // Add cache version field
        add_settings_field(
            'q_pwa_cache_version',
            'Cache Version',
            [self::class, 'render_text_field'],
            'q_pwa_settings',
            'q_pwa_basic_section',
            [
                'field' => 'q_pwa_cache_version',
                'description' => 'Change this value to force all clients to update their cache. Current value: ' . get_option('q_pwa_cache_version', 'v2')
            ]
        );
    }

    /**
     * Add Windows Control Overlay settings fields
     */
    private static function add_wco_settings_fields()
    {
        // Windows Control Overlay settings
        add_settings_field(
            'q_pwa_window_controls_overlay',
            'Enable Windows Control Overlay',
            [self::class, 'render_checkbox_field'],
            'q_pwa_settings',
            'q_pwa_advanced_section',
            ['field' => 'q_pwa_window_controls_overlay', 'description' => 'Enable custom title bar for PWA on Windows (desktop PWA only)']
        );

        add_settings_field(
            'q_pwa_wco_caption_button_close',
            'Caption Button Close Text',
            [self::class, 'render_text_field'],
            'q_pwa_settings',
            'q_pwa_advanced_section',
            ['field' => 'q_pwa_wco_caption_button_close', 'description' => 'Optional text for the close button in the title bar (leave empty for default)']
        );
    }

    /**
     * Add Related Applications settings fields
     */
    private static function add_related_apps_settings_fields()
    {
        // Add a section header for Related Applications
        add_settings_field(
            'q_pwa_related_apps_header',
            '<h3>Related Applications</h3>',
            function () {
                echo '<hr>';
            },
            'q_pwa_settings',
            'q_pwa_advanced_section'
        );

        // Related Applications settings
        add_settings_field(
            'q_pwa_related_apps_enabled',
            'Enable Related Applications',
            [self::class, 'render_checkbox_field'],
            'q_pwa_settings',
            'q_pwa_advanced_section',
            ['field' => 'q_pwa_related_apps_enabled', 'description' => 'Enable related applications in the manifest']
        );

        add_settings_field(
            'q_pwa_prefer_related_apps',
            'Prefer Related Applications',
            [self::class, 'render_checkbox_field'],
            'q_pwa_settings',
            'q_pwa_advanced_section',
            ['field' => 'q_pwa_prefer_related_apps', 'description' => 'Suggest that the user should use the related native application instead of the web app']
        );

        // Add fields for up to 5 related applications
        for ($i = 1; $i <= 5; $i++) {
            add_settings_field(
                "q_pwa_related_app_{$i}_header",
                "Related Application #{$i}",
                function () use ($i) {
                    echo "<hr><h4>Related Application #{$i}</h4>";
                },
                'q_pwa_settings',
                'q_pwa_advanced_section'
            );

            add_settings_field(
                "q_pwa_related_app_{$i}_platform",
                "Platform",
                [self::class, 'render_select_field'],
                'q_pwa_settings',
                'q_pwa_advanced_section',
                [
                    'field' => "q_pwa_related_app_{$i}_platform",
                    'options' => [
                        '' => 'Select Platform',
                        'play' => 'Google Play',
                        'itunes' => 'Apple App Store',
                        'windows' => 'Microsoft Store',
                        'webapp' => 'Web App',
                        'chrome_web_store' => 'Chrome Web Store',
                        'f-droid' => 'F-Droid'
                    ],
                    'description' => 'The platform on which the app is hosted'
                ]
            );

            add_settings_field(
                "q_pwa_related_app_{$i}_url",
                "URL",
                [self::class, 'render_text_field'],
                'q_pwa_settings',
                'q_pwa_advanced_section',
                ['field' => "q_pwa_related_app_{$i}_url", 'description' => 'The URL to the app (e.g., https://play.google.com/store/apps/details?id=com.example.app)']
            );

            add_settings_field(
                "q_pwa_related_app_{$i}_id",
                "App ID",
                [self::class, 'render_text_field'],
                'q_pwa_settings',
                'q_pwa_advanced_section',
                ['field' => "q_pwa_related_app_{$i}_id", 'description' => 'The ID of the app (e.g., com.example.app)']
            );
        }
    }


    private static function add_settings_fields()
    {
        // Basic Settings Fields
        add_settings_field(
            'q_pwa_enabled',
            'Enable PWA',
            [self::class, 'render_checkbox_field'],
            'q_pwa_settings',
            'q_pwa_basic_section',
            ['field' => 'q_pwa_enabled', 'description' => 'Enable Progressive Web App functionality']
        );

        add_settings_field(
            'q_pwa_app_name',
            'App Name',
            [self::class, 'render_text_field'],
            'q_pwa_settings',
            'q_pwa_basic_section',
            ['field' => 'q_pwa_app_name', 'description' => 'Full name of your app (max 45 characters)']
        );

        add_settings_field(
            'q_pwa_app_short_name',
            'Short Name',
            [self::class, 'render_text_field'],
            'q_pwa_settings',
            'q_pwa_basic_section',
            ['field' => 'q_pwa_app_short_name', 'description' => 'Short name for home screen (max 12 characters)']
        );

        add_settings_field(
            'q_pwa_app_description',
            'Description',
            [self::class, 'render_textarea_field'],
            'q_pwa_settings',
            'q_pwa_basic_section',
            ['field' => 'q_pwa_app_description', 'description' => 'Brief description of your app']
        );

        add_settings_field(
            'q_pwa_start_url',
            'Start URL',
            [self::class, 'render_text_field'],
            'q_pwa_settings',
            'q_pwa_basic_section',
            ['field' => 'q_pwa_start_url', 'description' => 'URL to load when app is launched (relative to site root)']
        );

        // Appearance Settings Fields
        add_settings_field(
            'q_pwa_theme_color',
            'Theme Color',
            [self::class, 'render_color_field'],
            'q_pwa_settings',
            'q_pwa_appearance_section',
            ['field' => 'q_pwa_theme_color', 'description' => 'Primary theme color for the app']
        );

        add_settings_field(
            'q_pwa_background_color',
            'Background Color',
            [self::class, 'render_color_field'],
            'q_pwa_settings',
            'q_pwa_appearance_section',
            ['field' => 'q_pwa_background_color', 'description' => 'Background color for splash screen']
        );

        add_settings_field(
            'q_pwa_splash_theme_color',
            'Splash Theme Color',
            [self::class, 'render_color_field'],
            'q_pwa_settings',
            'q_pwa_appearance_section',
            ['field' => 'q_pwa_splash_theme_color', 'description' => 'Theme color specifically for splash screen (leave empty to use main theme color)']
        );

        add_settings_field(
            'q_pwa_display_mode',
            'Display Mode',
            [self::class, 'render_select_field'],
            'q_pwa_settings',
            'q_pwa_appearance_section',
            [
                'field' => 'q_pwa_display_mode',
                'options' => [
                    'standalone' => 'Standalone (Recommended)',
                    'fullscreen' => 'Fullscreen',
                    'minimal-ui' => 'Minimal UI',
                    'browser' => 'Browser'
                ],
                'description' => 'How the app should be displayed when launched'
            ]
        );

        add_settings_field(
            'q_pwa_orientation',
            'Orientation',
            [self::class, 'render_select_field'],
            'q_pwa_settings',
            'q_pwa_appearance_section',
            [
                'field' => 'q_pwa_orientation',
                'options' => [
                    'any' => 'Any',
                    'portrait' => 'Portrait',
                    'landscape' => 'Landscape'
                ],
                'description' => 'Preferred screen orientation'
            ]
        );

        // Icon Settings Fields (remain in their original section)
        add_settings_field(
            'q_pwa_icon_192',
            '192x192 Icon',
            [self::class, 'render_media_field'],
            'q_pwa_settings',
            'q_pwa_icons_section',
            ['field' => 'q_pwa_icon_192', 'description' => 'App icon for Android devices (192x192 pixels)']
        );

        add_settings_field(
            'q_pwa_icon_512',
            '512x512 Icon',
            [self::class, 'render_media_field'],
            'q_pwa_settings',
            'q_pwa_icons_section',
            ['field' => 'q_pwa_icon_512', 'description' => 'App icon for splash screen (512x512 pixels)']
        );

        add_settings_field(
            'q_pwa_splash',
            'Splash Screen Image',
            [self::class, 'render_media_field'],
            'q_pwa_settings',
            'q_pwa_icons_section',
            ['field' => 'q_pwa_splash', 'description' => 'Dedicated splash screen image (recommended: 1125x2436 pixels for optimal display)']
        );

        // PWA Screenshots Fields
        add_settings_field(
            'q_pwa_screenshot_wide',
            'Wide Screenshot',
            [self::class, 'render_media_field'],
            'q_pwa_settings',
            'q_pwa_icons_section',
            ['field' => 'q_pwa_screenshot_wide', 'description' => 'Screenshot for wide/desktop displays (recommended: 1280x720 pixels)']
        );

        add_settings_field(
            'q_pwa_screenshot_narrow',
            'Narrow Screenshot',
            [self::class, 'render_media_field'],
            'q_pwa_settings',
            'q_pwa_icons_section',
            ['field' => 'q_pwa_screenshot_narrow', 'description' => 'Screenshot for narrow/mobile displays (recommended: 640x1136 pixels)']
        );

        // Desktop Settings Fields
        add_settings_field(
            'q_pwa_installable_on_desktop',
            'Allow Install on Desktop',
            [self::class, 'render_radio_field'],
            'q_pwa_settings',
            'q_pwa_advanced_section',
            [
                'field' => 'q_pwa_installable_on_desktop',
                'options' => [
                    1 => 'Yes',
                    0 => 'No',
                ],
                'description' => 'Allow users to install the PWA on desktop devices.'
            ]
        );

        // Add related applications settings fields - must be called here to ensure proper tab display
        self::add_related_apps_settings_fields();
    }

    public static function render_pwa_settings_page()
    {
        // Inject variables for the subscribers tab
        $subscribers = get_users(array(
            'meta_key' => 'q_push_token',
            'meta_value' => '',
            'meta_compare' => '!='
        ));
        $total_notifications = function_exists('q_get_total_notifications') ? q_get_total_notifications() : 0;
        $engagement_rate = function_exists('q_get_engagement_rate') ? q_get_engagement_rate() : 0;
        require_once Q_PLUGIN_DIR . 'includes/templates/pwa-app-layout.php';
    }

    // Section descriptions
    public static function render_basic_section_description()
    {
        echo '<p>Configure basic Progressive Web App settings for your site.</p>';
    }

    public static function render_appearance_section_description()
    {
        echo '<p>Customize how your PWA looks and behaves when installed.</p>';
    }

    public static function render_icons_section_description()
    {
        echo '<p>Upload icons, splash screens, and screenshots for your PWA. Icons should be square and in PNG format.</p>';
    }

    public static function render_advanced_section_description()
    {
        echo '<p>Configure advanced PWA features like app shortcuts and protocol handlers.</p>';
    }

    public static function render_analytics_section_description()
    {
        echo '<p>Configure settings related to PWA analytics and view usage statistics.</p>';
    }

    public static function render_sync_section_description()
    {
        echo '<p>Configure periodic background sync to keep your app updated even when it\'s not open.</p>';
    }

    // Field renderers
    public static function render_text_field($args)
    {
        $field = $args['field'];
        $value = get_option($field, '');
        $description = $args['description'] ?? '';

        echo '<input type="text" name="' . esc_attr($field) . '" value="' . esc_attr($value) . '" class="regular-text" />';
        if ($description) {
            echo '<p class="description">' . esc_html($description) . '</p>';
        }
    }

    public static function render_textarea_field($args)
    {
        $field = $args['field'];
        $value = get_option($field, '');
        $description = $args['description'] ?? '';

        echo '<textarea name="' . esc_attr($field) . '" rows="3" cols="50" class="large-text">' . esc_textarea($value) . '</textarea>';
        if ($description) {
            echo '<p class="description">' . esc_html($description) . '</p>';
        }
    }

    public static function render_checkbox_field($args)
    {
        $field       = $args['field'];
        $value       = get_option($field, false);
        $description = $args['description'] ?? '';

        // Ensure the option gets updated when the box is unchecked by adding a hidden fallback field.
        echo '<input type="hidden" name="' . esc_attr($field) . '" value="0" />';

        // Render the actual checkbox.
        echo '<label style="display:inline-flex;align-items:center;gap:6px;">';
        echo '<input type="checkbox" name="' . esc_attr($field) . '" value="1" ' . checked(1, $value, false) . ' />';
        if ($description) {
            echo '<span>' . esc_html($description) . '</span>';
        }
        echo '</label>';
    }

    public static function render_color_field($args)
    {
        $field = $args['field'];
        $value = get_option($field, '#ffffff');
        $description = $args['description'] ?? '';

        echo '<input type="color" name="' . esc_attr($field) . '" value="' . esc_attr($value) . '" />';
        if ($description) {
            echo '<p class="description">' . esc_html($description) . '</p>';
        }
    }

    public static function render_select_field($args)
    {
        $field = $args['field'];
        $value = get_option($field, '');
        $options = $args['options'] ?? [];
        $description = $args['description'] ?? '';

        echo '<select name="' . esc_attr($field) . '">';
        foreach ($options as $option_value => $option_label) {
            echo '<option value="' . esc_attr($option_value) . '" ' . selected($value, $option_value, false) . '>' . esc_html($option_label) . '</option>';
        }
        echo '</select>';
        if ($description) {
            echo '<p class="description">' . esc_html($description) . '</p>';
        }
    }

    public static function render_media_field($args)
    {
        $field = $args['field'];
        $value = get_option($field, '');
        $description = $args['description'] ?? '';

        echo '<input type="url" name="' . esc_attr($field) . '" value="' . esc_attr($value) . '" class="regular-text" />';
        echo '<button type="button" class="button q-media-upload" data-field="' . esc_attr($field) . '">Upload Image</button>';
        if ($value) {
            echo '<br><img src="' . esc_url($value) . '" style="max-width: 100px; max-height: 100px; margin-top: 10px;" />';
        }
        if ($description) {
            echo '<p class="description">' . esc_html($description) . '</p>';
        }
    }

    /**
     * Render a dropdown of WordPress pages for selecting the offline fallback page
     */
    public static function render_page_dropdown_field($args)
    {
        $field = $args['field'];
        $value = get_option($field, 0);
        $description = $args['description'] ?? '';
        $pages = get_pages(['post_status' => 'publish']);
        echo '<select name="' . esc_attr($field) . '">';
        echo '<option value="0">-- None --</option>';
        foreach ($pages as $page) {
            $selected = selected($value, $page->ID, false);
            echo '<option value="' . esc_attr($page->ID) . '" ' . $selected . '>' . esc_html($page->post_title) . '</option>';
        }
        echo '</select>';
        if ($description) {
            echo '<p class="description">' . esc_html($description) . '</p>';
        }
    }

    // Sanitization functions
    public static function sanitize_display_mode($value)
    {
        $allowed = ['standalone', 'fullscreen', 'minimal-ui', 'browser'];
        return in_array($value, $allowed) ? $value : 'standalone';
    }

    public static function sanitize_orientation($value)
    {
        $allowed = ['any', 'portrait', 'landscape'];
        return in_array($value, $allowed) ? $value : 'any';
    }

    // Frontend functionality

    public static function add_pwa_meta_tags()
    {
        if (!get_option('q_pwa_enabled', false)) {
            return;
        }

        $theme_color = get_option('q_pwa_theme_color', '#ffffff');
        $app_name = get_option('q_pwa_app_name', get_bloginfo('name'));

        echo '<meta name="theme-color" content="' . esc_attr($theme_color) . '">' . "\n";

        // --------------------------------------------------
        // iOS specific meta tags (configurable via settings)
        // --------------------------------------------------
        // Always enable apple-mobile-web-app-capable for iOS to fix blank screen issue
        echo '<meta name="apple-mobile-web-app-capable" content="yes">' . "\n";

        $ios_status_bar = get_option('q_pwa_ios_status_bar_style', 'default');
        echo '<meta name="apple-mobile-web-app-status-bar-style" content="' . esc_attr($ios_status_bar) . '">' . "\n";
        echo '<meta name="apple-mobile-web-app-title" content="' . esc_attr($app_name) . '">' . "\n";

        // Android / generic Meta
        echo '<meta name="mobile-web-app-capable" content="yes">' . "\n";

        // Navigation bar colour (Android 8.0+ & Windows task-bar) if different from theme colour
        $nav_color = get_option('q_pwa_nav_color', '');
        if (!empty($nav_color)) {
            echo '<meta name="msapplication-navbutton-color" content="' . esc_attr($nav_color) . '">' . "\n";
        }

        // Add iOS specific viewport meta tag for splash screen (with viewport-fit=cover for notched devices)
        echo '<meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover, shrink-to-fit=no, user-scalable=no">' . "\n";

        // Add Windows Control Overlay meta tag if enabled
        if (get_option('q_pwa_window_controls_overlay', false)) {
            echo '<meta name="theme-color" media="(prefers-color-scheme: light)" content="' . esc_attr($theme_color) . '">' . "\n";
            echo '<meta name="theme-color" media="(prefers-color-scheme: dark)" content="' . esc_attr($theme_color) . '">' . "\n";
        }

        // Add iOS splash screen meta tags
        self::add_ios_splash_screen_meta_tags();

        // Add iOS splash screen background color
        $background_color = get_option('q_pwa_background_color', '#ffffff');
        echo '<meta name="apple-mobile-web-app-status-bar-style" content="' . (self::is_dark_color($background_color) ? 'black-translucent' : 'default') . '">' . "\n";

        // Add Apple touch icon if available or fallback to main icon
        $apple_touch_icon = get_option('q_pwa_apple_touch_icon', '');
        $main_icon = get_option('q_pwa_icon', '');
        $icon_192 = get_option('q_pwa_icon_192', '');
        $icon_512 = get_option('q_pwa_icon_512', '');

        // Determine the best icon to use for Apple Touch Icon
        $touch_icon_src = '';
        if (!empty($apple_touch_icon)) {
            $touch_icon_src = $apple_touch_icon;
        } elseif (!empty($icon_512)) {
            $touch_icon_src = $icon_512;
        } elseif (!empty($icon_192)) {
            $touch_icon_src = $icon_192;
        } elseif (!empty($main_icon)) {
            $touch_icon_src = $main_icon;
        }

        if (!empty($touch_icon_src)) {
            // Ensure URL is absolute
            if (!preg_match('/^https?:\/\//', $touch_icon_src)) {
                $touch_icon_src = site_url($touch_icon_src);
            }

            // Add the most important sizes for iOS PWA launch
            $critical_sizes = ['180x180', '152x152', '144x144', '120x120', '114x114', '76x76', '72x72', '60x60', '57x57'];
            foreach ($critical_sizes as $size) {
                echo '<link rel="apple-touch-icon" sizes="' . $size . '" href="' . esc_url($touch_icon_src) . '">' . "\n";
            }

            // Add the default one without size (most important for PWA launch)
            echo '<link rel="apple-touch-icon" href="' . esc_url($touch_icon_src) . '">' . "\n";

            // Add apple-touch-icon-precomposed for older iOS versions
            echo '<link rel="apple-touch-icon-precomposed" sizes="180x180" href="' . esc_url($touch_icon_src) . '">' . "\n";
            echo '<link rel="apple-touch-icon-precomposed" href="' . esc_url($touch_icon_src) . '">' . "\n";

            // Add shortcut icon for better compatibility
            echo '<link rel="shortcut icon" href="' . esc_url($touch_icon_src) . '">' . "\n";
        }

        // --------------------------------------------------
        // Favicon / generic icon links (desktop browsers)
        // --------------------------------------------------
        if (!empty($main_icon)) {
            $icon32 = esc_url($main_icon);
            echo '<link rel="icon" type="image/png" sizes="32x32" href="' . $icon32 . '">' . "\n";
            // Use 192x192 if provided
            $icon192 = get_option('q_pwa_icon_192', $main_icon);
            if (!empty($icon192)) {
                if (!preg_match('/^https?:\/\//', $icon192)) {
                    $icon192 = site_url($icon192);
                }
                echo '<link rel="icon" type="image/png" sizes="192x192" href="' . esc_url($icon192) . '">' . "\n";
            }
        }

        // Add critical iOS PWA meta tags for proper icon display
        if (!empty($touch_icon_src)) {
            // Add mask-icon for Safari pinned tabs
            echo '<link rel="mask-icon" href="' . esc_url($touch_icon_src) . '" color="' . esc_attr($theme_color) . '">' . "\n";

            // Add msapplication icons for Windows
            echo '<meta name="msapplication-TileImage" content="' . esc_url($touch_icon_src) . '">' . "\n";
            echo '<meta name="msapplication-TileColor" content="' . esc_attr($theme_color) . '">' . "\n";
        }

        // Add a script to fix iOS blank screen issue and ensure icon loading
        echo '<script>
        // Fix for iOS PWA blank screen issue and icon loading
        (function() {
            var isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
            var isStandalone = window.navigator.standalone === true;
            
            if (isIOS) {
                // Force body display for iOS PWA
                if (isStandalone) {
                    document.documentElement.style.display = "block";
                    document.body.style.display = "block";
                }
                
                // Preload critical icons
                var iconUrl = "' . esc_js($touch_icon_src) . '";
                if (iconUrl) {
                    var link = document.createElement("link");
                    link.rel = "preload";
                    link.href = iconUrl;
                    link.as = "image";
                    document.head.appendChild(link);
                }
                
                // Ensure proper viewport for iOS PWA
                var viewport = document.querySelector("meta[name=viewport]");
                if (viewport && isStandalone) {
                    viewport.content = "width=device-width, initial-scale=1.0, viewport-fit=cover, shrink-to-fit=no, user-scalable=no";
                }
            }
            
            // Fix for iOS PWA blank screen on launch
            if (isIOS && isStandalone) {
                window.addEventListener("DOMContentLoaded", function() {
                    document.body.style.display = "block";
                    document.documentElement.style.display = "block";
                    
                    // Force repaint
                    document.body.offsetHeight;
                });
                
                // Immediate fix
                document.addEventListener("readystatechange", function() {
                    if (document.readyState === "interactive" || document.readyState === "complete") {
                        document.body.style.display = "block";
                        document.documentElement.style.display = "block";
                    }
                });
            }
        })();
        </script>' . "\n";
    }

    public static function add_manifest_link()
    {
        if (!get_option('q_pwa_enabled', false)) {
            return;
        }

        // Add a cache-busting parameter using the manifest update timestamp
        $version = get_option('q_pwa_manifest_updated', time());
        $cache_buster = '?v=' . $version;

        echo '<link rel="manifest" href="' . esc_url(home_url('/manifest.json' . $cache_buster)) . '">' . "\n";

        // Add a script to force manifest reload
        echo '<script>
        if ("serviceWorker" in navigator) {
            window.addEventListener("load", function() {
                // Force manifest reload
                if (navigator.serviceWorker.controller) {
                    try {
                        navigator.serviceWorker.controller.postMessage({
                            type: "CLEAR_MANIFEST_CACHE",
                            version: "' . $version . '"
                        });
                    } catch(e) {
                        console.log("Error sending message to service worker:", e);
                    }
                }
                
                // Fetch the manifest directly to ensure it\'s updated in the browser cache
                fetch("/manifest.json' . $cache_buster . '", {
                    cache: "no-cache",
                    headers: {
                        "Cache-Control": "no-cache, no-store, must-revalidate",
                        "Pragma": "no-cache",
                        "Expires": "0"
                    }
                }).then(function(response) {
                    console.log("Manifest refreshed:", response.status);
                    // Force PWA to update
                    if ("serviceWorker" in navigator && navigator.serviceWorker.controller) {
                        navigator.serviceWorker.controller.postMessage({
                            type: "SKIP_WAITING"
                        });
                    }
                }).catch(function(err) {
                    console.log("Error fetching manifest:", err);
                });
            });
        }
        </script>' . "\n";
    }

    /**
     * Add iOS splash screen meta tags for various device sizes
     */
    public static function add_ios_splash_screen_meta_tags()
    {
        $splash_image = get_option('q_pwa_splash', '');
        if (empty($splash_image)) {
            return;
        }

        // Ensure the image URL is absolute
        if (!preg_match('/^https?:\/\//', $splash_image)) {
            $splash_image = site_url($splash_image);
        }

        // iOS splash screen sizes for different devices and orientations
        $splash_sizes = [
            // iPhone SE, 5s, 5c, 5 (Portrait)
            ['width' => 640, 'height' => 1136, 'device_width' => 320, 'device_height' => 568, 'orientation' => 'portrait', 'pixel_ratio' => 2],
            // iPhone SE, 5s, 5c, 5 (Landscape)
            ['width' => 1136, 'height' => 640, 'device_width' => 568, 'device_height' => 320, 'orientation' => 'landscape', 'pixel_ratio' => 2],

            // iPhone 8, 7, 6s, 6 (Portrait)
            ['width' => 750, 'height' => 1334, 'device_width' => 375, 'device_height' => 667, 'orientation' => 'portrait', 'pixel_ratio' => 2],
            // iPhone 8, 7, 6s, 6 (Landscape)
            ['width' => 1334, 'height' => 750, 'device_width' => 667, 'device_height' => 375, 'orientation' => 'landscape', 'pixel_ratio' => 2],

            // iPhone 8 Plus, 7 Plus, 6s Plus, 6 Plus (Portrait)
            ['width' => 1242, 'height' => 2208, 'device_width' => 414, 'device_height' => 736, 'orientation' => 'portrait', 'pixel_ratio' => 3],
            // iPhone 8 Plus, 7 Plus, 6s Plus, 6 Plus (Landscape)
            ['width' => 2208, 'height' => 1242, 'device_width' => 736, 'device_height' => 414, 'orientation' => 'landscape', 'pixel_ratio' => 3],

            // iPhone X, XS, 11 Pro (Portrait)
            ['width' => 1125, 'height' => 2436, 'device_width' => 375, 'device_height' => 812, 'orientation' => 'portrait', 'pixel_ratio' => 3],
            // iPhone X, XS, 11 Pro (Landscape)
            ['width' => 2436, 'height' => 1125, 'device_width' => 812, 'device_height' => 375, 'orientation' => 'landscape', 'pixel_ratio' => 3],

            // iPhone XR, 11 (Portrait)
            ['width' => 828, 'height' => 1792, 'device_width' => 414, 'device_height' => 896, 'orientation' => 'portrait', 'pixel_ratio' => 2],
            // iPhone XR, 11 (Landscape)
            ['width' => 1792, 'height' => 828, 'device_width' => 896, 'device_height' => 414, 'orientation' => 'landscape', 'pixel_ratio' => 2],

            // iPhone XS Max, 11 Pro Max (Portrait)
            ['width' => 1242, 'height' => 2688, 'device_width' => 414, 'device_height' => 896, 'orientation' => 'portrait', 'pixel_ratio' => 3],
            // iPhone XS Max, 11 Pro Max (Landscape)
            ['width' => 2688, 'height' => 1242, 'device_width' => 896, 'device_height' => 414, 'orientation' => 'landscape', 'pixel_ratio' => 3],

            // iPhone 12 mini, 13 mini (Portrait)
            ['width' => 1080, 'height' => 2340, 'device_width' => 375, 'device_height' => 812, 'orientation' => 'portrait', 'pixel_ratio' => 3],
            // iPhone 12 mini, 13 mini (Landscape)
            ['width' => 2340, 'height' => 1080, 'device_width' => 812, 'device_height' => 375, 'orientation' => 'landscape', 'pixel_ratio' => 3],

            // iPhone 12, 12 Pro, 13, 13 Pro (Portrait)
            ['width' => 1170, 'height' => 2532, 'device_width' => 390, 'device_height' => 844, 'orientation' => 'portrait', 'pixel_ratio' => 3],
            // iPhone 12, 12 Pro, 13, 13 Pro (Landscape)
            ['width' => 2532, 'height' => 1170, 'device_width' => 844, 'device_height' => 390, 'orientation' => 'landscape', 'pixel_ratio' => 3],

            // iPhone 12 Pro Max, 13 Pro Max (Portrait)
            ['width' => 1284, 'height' => 2778, 'device_width' => 428, 'device_height' => 926, 'orientation' => 'portrait', 'pixel_ratio' => 3],
            // iPhone 12 Pro Max, 13 Pro Max (Landscape)
            ['width' => 2778, 'height' => 1284, 'device_width' => 926, 'device_height' => 428, 'orientation' => 'landscape', 'pixel_ratio' => 3],

            // iPad (Portrait)
            ['width' => 1536, 'height' => 2048, 'device_width' => 768, 'device_height' => 1024, 'orientation' => 'portrait', 'pixel_ratio' => 2],
            // iPad (Landscape)
            ['width' => 2048, 'height' => 1536, 'device_width' => 1024, 'device_height' => 768, 'orientation' => 'landscape', 'pixel_ratio' => 2],

            // iPad Pro 10.5" (Portrait)
            ['width' => 1668, 'height' => 2224, 'device_width' => 834, 'device_height' => 1112, 'orientation' => 'portrait', 'pixel_ratio' => 2],
            // iPad Pro 10.5" (Landscape)
            ['width' => 2224, 'height' => 1668, 'device_width' => 1112, 'device_height' => 834, 'orientation' => 'landscape', 'pixel_ratio' => 2],

            // iPad Pro 12.9" (Portrait)
            ['width' => 2048, 'height' => 2732, 'device_width' => 1024, 'device_height' => 1366, 'orientation' => 'portrait', 'pixel_ratio' => 2],
            // iPad Pro 12.9" (Landscape)
            ['width' => 2732, 'height' => 2048, 'device_width' => 1366, 'device_height' => 1024, 'orientation' => 'landscape', 'pixel_ratio' => 2],
        ];

        // Generate splash screen images and meta tags
        foreach ($splash_sizes as $size) {
            $splash_url = self::generate_splash_screen_image($splash_image, $size['width'], $size['height']);
            if ($splash_url) {
                $media_query = "(device-width: {$size['device_width']}px) and (device-height: {$size['device_height']}px) and (-webkit-device-pixel-ratio: {$size['pixel_ratio']}) and (orientation: {$size['orientation']})";
                echo '<link rel="apple-touch-startup-image" href="' . esc_url($splash_url) . '" media="' . esc_attr($media_query) . '">' . "\n";
            }
        }

        // Add fallback splash screen for devices not covered above
        $fallback_splash = self::generate_splash_screen_image($splash_image, 1125, 2436); // iPhone X size as fallback
        if ($fallback_splash) {
            echo '<link rel="apple-touch-startup-image" href="' . esc_url($fallback_splash) . '">' . "\n";
        }
    }

    /**
     * Generate splash screen image for specific dimensions
     */
    public static function generate_splash_screen_image($source_url, $width, $height)
    {
        // Check if we already have a cached version
        $cache_key = 'splash_' . md5($source_url . $width . $height);
        $cached_url = get_transient($cache_key);
        if ($cached_url && self::url_exists($cached_url)) {
            return $cached_url;
        }

        // Get WordPress upload directory
        $upload_dir = wp_upload_dir();
        if (!$upload_dir || isset($upload_dir['error'])) {
            return false;
        }

        // Create splash screen directory if it doesn't exist
        $splash_dir = trailingslashit($upload_dir['basedir']) . 'pwa-splash/';
        if (!file_exists($splash_dir)) {
            wp_mkdir_p($splash_dir);
        }

        // Download source image if it's a URL
        $source_path = $source_url;
        if (preg_match('/^https?:\/\//', $source_url)) {
            $temp_file = download_url($source_url);
            if (is_wp_error($temp_file)) {
                return false;
            }
            $source_path = $temp_file;
        } else {
            // Convert relative path to absolute
            $source_path = str_replace(home_url('/'), ABSPATH, $source_url);
        }

        if (!file_exists($source_path)) {
            return false;
        }

        // Create image editor
        $editor = wp_get_image_editor($source_path);
        if (is_wp_error($editor)) {
            if (isset($temp_file)) {
                unlink($temp_file);
            }
            return false;
        }

        // Get current image dimensions
        $current_size = $editor->get_size();
        if (is_wp_error($current_size)) {
            if (isset($temp_file)) {
                unlink($temp_file);
            }
            return false;
        }

        // Calculate scaling to fit within target dimensions while maintaining aspect ratio
        $scale_x = $width / $current_size['width'];
        $scale_y = $height / $current_size['height'];
        $scale = min($scale_x, $scale_y);

        $new_width = round($current_size['width'] * $scale);
        $new_height = round($current_size['height'] * $scale);

        // Resize the image
        $resize_result = $editor->resize($new_width, $new_height, false);
        if (is_wp_error($resize_result)) {
            if (isset($temp_file)) {
                unlink($temp_file);
            }
            return false;
        }

        // Create a new canvas with the target dimensions and background color
        $background_color = get_option('q_pwa_background_color', '#ffffff');

        // Create new image with target dimensions
        $canvas = imagecreatetruecolor($width, $height);

        // Set background color
        $bg_color = self::hex_to_rgb($background_color);
        $bg = imagecolorallocate($canvas, $bg_color['r'], $bg_color['g'], $bg_color['b']);
        imagefill($canvas, 0, 0, $bg);

        // Get the resized image resource
        $resized_image = $editor->get_image();
        if (!$resized_image) {
            imagedestroy($canvas);
            if (isset($temp_file)) {
                unlink($temp_file);
            }
            return false;
        }

        // Center the resized image on the canvas
        $x = ($width - $new_width) / 2;
        $y = ($height - $new_height) / 2;

        imagecopy($canvas, $resized_image, $x, $y, 0, 0, $new_width, $new_height);

        // Save the final image
        $filename = 'splash-' . $width . 'x' . $height . '-' . uniqid() . '.png';
        $filepath = $splash_dir . $filename;
        $fileurl = trailingslashit($upload_dir['url']) . 'pwa-splash/' . $filename;

        $saved = imagepng($canvas, $filepath);

        // Clean up
        imagedestroy($canvas);
        imagedestroy($resized_image);
        if (isset($temp_file)) {
            unlink($temp_file);
        }

        if (!$saved) {
            return false;
        }

        // Cache the result for 24 hours
        set_transient($cache_key, $fileurl, DAY_IN_SECONDS);

        return $fileurl;
    }

    /**
     * Convert hex color to RGB array
     */
    private static function hex_to_rgb($hex)
    {
        $hex = ltrim($hex, '#');
        if (strlen($hex) == 3) {
            $hex = $hex[0] . $hex[0] . $hex[1] . $hex[1] . $hex[2] . $hex[2];
        }
        return [
            'r' => hexdec(substr($hex, 0, 2)),
            'g' => hexdec(substr($hex, 2, 2)),
            'b' => hexdec(substr($hex, 4, 2))
        ];
    }

    /**
     * Check if URL exists and is accessible
     */
    private static function url_exists($url)
    {
        $response = wp_remote_head($url);
        return !is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200;
    }

    /**
     * Determine if a color is dark
     */
    private static function is_dark_color($hex_color)
    {
        $rgb = self::hex_to_rgb($hex_color);
        $brightness = (($rgb['r'] * 299) + ($rgb['g'] * 587) + ($rgb['b'] * 114)) / 1000;
        return $brightness < 128;
    }

    /**
     * Validate splash screen configuration
     */
    public static function validate_splash_screen_config()
    {
        $validation_results = [
            'valid' => true,
            'errors' => [],
            'warnings' => [],
            'info' => []
        ];

        // Check if PWA is enabled
        if (!get_option('q_pwa_enabled', false)) {
            $validation_results['errors'][] = 'PWA is not enabled';
            $validation_results['valid'] = false;
            return $validation_results;
        }

        // Check splash screen image
        $splash_image = get_option('q_pwa_splash', '');
        $app_icon = get_option('q_pwa_icon', '');

        if (empty($splash_image) && empty($app_icon)) {
            $validation_results['errors'][] = 'No splash screen image or app icon configured';
            $validation_results['valid'] = false;
        } elseif (empty($splash_image)) {
            $validation_results['warnings'][] = 'No dedicated splash screen image - using app icon as fallback';
            $validation_results['info'][] = 'Consider uploading a dedicated splash screen image for better visual experience';
        }

        // Validate splash screen image if present
        if (!empty($splash_image)) {
            // Make URL absolute
            $splash_url = $splash_image;
            if (!preg_match('/^https?:\/\//', $splash_url)) {
                $splash_url = site_url($splash_url);
            }

            // Check if image is accessible
            if (!self::url_exists($splash_url)) {
                $validation_results['errors'][] = 'Splash screen image is not accessible: ' . $splash_url;
                $validation_results['valid'] = false;
            } else {
                // Check image dimensions
                $image_size = getimagesize($splash_url);
                if ($image_size) {
                    $width = $image_size[0];
                    $height = $image_size[1];

                    $validation_results['info'][] = "Splash screen image dimensions: {$width}x{$height}";

                    // Recommend minimum dimensions
                    if ($width < 1024 || $height < 1024) {
                        $validation_results['warnings'][] = 'Splash screen image is smaller than recommended (1024x1024 minimum)';
                    }

                    // Check aspect ratio for mobile optimization
                    $aspect_ratio = $width / $height;
                    if ($aspect_ratio < 0.5 || $aspect_ratio > 2.0) {
                        $validation_results['info'][] = 'Consider using an image with aspect ratio closer to 1:1 or 16:9 for better mobile compatibility';
                    }
                } else {
                    $validation_results['warnings'][] = 'Could not determine splash screen image dimensions';
                }
            }
        }

        // Check background color
        $background_color = get_option('q_pwa_background_color', '#ffffff');
        if (!preg_match('/^#[0-9A-Fa-f]{6}$/', $background_color)) {
            $validation_results['warnings'][] = 'Invalid background color format - using default white';
        }

        // Check theme colors
        $theme_color = get_option('q_pwa_theme_color', '#FFFFFF');
        if (!preg_match('/^#[0-9A-Fa-f]{6}$/', $theme_color)) {
            $validation_results['warnings'][] = 'Invalid theme color format - using default black';
        }

        $splash_theme_color = get_option('q_pwa_splash_theme_color', '#FFFFFF');
        if (!empty($splash_theme_color) && !preg_match('/^#[0-9A-Fa-f]{6}$/', $splash_theme_color)) {
            $validation_results['warnings'][] = 'Invalid splash theme color format - falling back to main theme color';
        }

        // Check iOS specific settings
        $ios_capable = get_option('q_pwa_ios_web_app_capable', false);
        if (!$ios_capable) {
            $validation_results['warnings'][] = 'iOS web app capable is disabled - iOS splash screens may not work properly';
        }

        // Check if HTTPS is enabled (required for PWA)
        if (!is_ssl()) {
            $validation_results['errors'][] = 'HTTPS is required for PWA splash screens to work properly';
            $validation_results['valid'] = false;
        }

        return $validation_results;
    }

    /**
     * Display splash screen validation results in admin
     */
    public static function display_splash_screen_validation()
    {
        $validation = self::validate_splash_screen_config();

        echo '<div class="q-pwa-validation-results">';
        echo '<h3>Splash Screen Configuration Status</h3>';

        if ($validation['valid']) {
            echo '<div class="notice notice-success"><p><strong>✓ Splash screen configuration is valid</strong></p></div>';
        } else {
            echo '<div class="notice notice-error"><p><strong>✗ Splash screen configuration has errors</strong></p></div>';
        }

        if (!empty($validation['errors'])) {
            echo '<div class="notice notice-error">';
            echo '<p><strong>Errors:</strong></p><ul>';
            foreach ($validation['errors'] as $error) {
                echo '<li>' . esc_html($error) . '</li>';
            }
            echo '</ul></div>';
        }

        if (!empty($validation['warnings'])) {
            echo '<div class="notice notice-warning">';
            echo '<p><strong>Warnings:</strong></p><ul>';
            foreach ($validation['warnings'] as $warning) {
                echo '<li>' . esc_html($warning) . '</li>';
            }
            echo '</ul></div>';
        }

        if (!empty($validation['info'])) {
            echo '<div class="notice notice-info">';
            echo '<p><strong>Information:</strong></p><ul>';
            foreach ($validation['info'] as $info) {
                echo '<li>' . esc_html($info) . '</li>';
            }
            echo '</ul></div>';
        }

        echo '</div>';
    }

    // Check PWA requirements
    public static function check_pwa_requirements()
    {
        $requirements = [
            'https' => is_ssl(),
            'manifest' => get_option('q_pwa_enabled', false),
            'service_worker' => file_exists(ABSPATH . 'firebase-messaging-sw.js'),
            'icons' => !empty(get_option('q_pwa_icon', ''))
                || (!empty(get_option('q_pwa_icon_192', '')) && !empty(get_option('q_pwa_icon_512', '')))
                || !empty(get_option('site_icon'))
        ];

        return $requirements;
    }

    // Get PWA status
    public static function get_pwa_status()
    {
        $requirements = self::check_pwa_requirements();
        $total = count($requirements);
        $met = count(array_filter($requirements));

        return [
            'requirements' => $requirements,
            'percentage' => round(($met / $total) * 100),
            'ready' => $met === $total
        ];
    }

    // Enqueue admin scripts and styles
    public static function enqueue_pwa_scripts($hook)
    {
        // For frontend, enqueue PWA scripts regardless of page
        if (!is_admin()) {
            // Enqueue PWA styles
            wp_enqueue_style(
                'q-pwa-styles',
                plugins_url('includes/css/pwa-styles.css', dirname(__FILE__)),
                array(),
                filemtime(Q_PLUGIN_DIR . 'includes/css/pwa-styles.css')
            );

            // Enqueue splash screen styles
            wp_enqueue_style(
                'q-pwa-splash-styles',
                plugins_url('includes/css/splash-screen.css', dirname(__FILE__)),
                array('q-pwa-styles'),
                filemtime(Q_PLUGIN_DIR . 'includes/css/splash-screen.css')
            );


            // Add immediate iOS PWA blank screen fix inline script (runs before any external scripts)
            wp_register_script('q-ios-pwa-immediate-fix', '', array(), '', false);
            wp_enqueue_script('q-ios-pwa-immediate-fix');
            wp_add_inline_script('q-ios-pwa-immediate-fix', '
                (function() {
                    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
                    const isStandalone = window.navigator.standalone === true;

                    if (isIOS && isStandalone) {
                        // Immediate fix for blank screen
                        document.documentElement.style.display = "block";
                        document.documentElement.style.visibility = "visible";
                        document.documentElement.style.opacity = "1";

                        if (document.body) {
                            document.body.style.display = "block";
                            document.body.style.visibility = "visible";
                            document.body.style.opacity = "1";
                        }
                    }
                })();
            ', 'before');

            // Enqueue iOS PWA fix script first (highest priority)
            wp_enqueue_script(
                'q-ios-pwa-fix',
                plugins_url('includes/js/ios-pwa-fix.js', dirname(__FILE__)),
                array(),
                filemtime(Q_PLUGIN_DIR . 'includes/js/ios-pwa-fix.js'),
                false // Load in header for immediate execution
            );

            // Set highest priority for iOS fix script
            wp_script_add_data('q-ios-pwa-fix', 'priority', 1);

            // Enqueue iOS PWA debug script for development (only if WP_DEBUG is enabled)
            if (defined('WP_DEBUG') && WP_DEBUG) {
                wp_enqueue_script(
                    'q-ios-pwa-debug',
                    plugins_url('includes/js/ios-pwa-debug.js', dirname(__FILE__)),
                    array(),
                    filemtime(Q_PLUGIN_DIR . 'includes/js/ios-pwa-debug.js'),
                    false // Load in header for immediate execution
                );
            }

            // Enqueue PWA manager script
            wp_enqueue_script(
                'q-pwa-manager',
                plugins_url('includes/js/pwa-manager.js', dirname(__FILE__)),
                array('jquery'),
                filemtime(Q_PLUGIN_DIR . 'includes/js/pwa-manager.js'),
                true
            );

            // Enqueue splash screen script
            wp_enqueue_script(
                'q-pwa-splash-screen',
                plugins_url('includes/js/splash-screen.js', dirname(__FILE__)),
                array('q-pwa-manager'),
                filemtime(Q_PLUGIN_DIR . 'includes/js/splash-screen.js'),
                true
            );

            // Enqueue analytics tracking script if enabled
            if (get_option('q_pwa_analytics_enabled', true)) {
                wp_enqueue_script(
                    'q-pwa-analytics-tracking',
                    plugins_url('includes/js/pwa-analytics-tracking.js', dirname(__FILE__)),
                    array('q-pwa-manager'),
                    filemtime(Q_PLUGIN_DIR . 'includes/js/pwa-analytics-tracking.js'),
                    true
                );
            }

            // Enqueue PWA shortcuts handler
            if (get_option('q_pwa_shortcuts_enabled', false)) {
                wp_enqueue_script(
                    'q-pwa-shortcuts',
                    plugins_url('includes/js/pwa-shortcuts.js', dirname(__FILE__)),
                    array('q-pwa-manager'),
                    filemtime(Q_PLUGIN_DIR . 'includes/js/pwa-shortcuts.js'),
                    true
                );
            }

            // Enqueue subscription toggle script
            wp_enqueue_script(
                'q-subscription-toggle',
                plugins_url('includes/js/subscription-toggle.js', dirname(__FILE__)),
                array('jquery'),
                filemtime(Q_PLUGIN_DIR . 'includes/js/subscription-toggle.js'),
                true
            );



            // ------------------------------------------------------------
            // Pass PWA settings to JavaScript
            // ------------------------------------------------------------
            // Ensure we have an absolute URL for the main PWA icon so it
            // can be used inside in-browser install banners.
            $icon_url = get_option('q_pwa_icon', '');
            if ($icon_url && !preg_match('/^https?:\/\//', $icon_url)) {
                $icon_url = site_url($icon_url);
            }

            $splash_url = get_option('q_pwa_splash', '');
            if ($splash_url && !preg_match('/^https?:\/\//', $splash_url)) {
                $splash_url = site_url($splash_url);
            }

            // Add CSS variables for PWA colors
            $pwa_background_color = get_option('q_pwa_background_color', '#ffffff');
            $pwa_theme_color = get_option('q_pwa_theme_color', '#ffffff');

            wp_add_inline_style('q-pwa-styles', "
                :root {
                    --pwa-background-color: {$pwa_background_color};
                    --pwa-theme-color: {$pwa_theme_color};
                }
            ");

            wp_localize_script('q-pwa-manager', 'qPWASettings', [
                'enabled' => (bool) get_option('q_pwa_enabled', false),
                'appName' => get_option('q_pwa_app_name', get_bloginfo('name')),
                'themeColor' => $pwa_theme_color,
                'backgroundColor' => $pwa_background_color,
                'cacheVersion' => get_option('q_pwa_cache_version', 'v1'),
                'cacheTTL' => 3600 * 24 * 7, // 1 week default
                'siteUrl' => home_url('/'),
                'ajaxUrl' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('q_pwa_nonce'),
                'windowControlsOverlay' => (bool) get_option('q_pwa_window_controls_overlay', false),
                'wcoCaptionButtonClose' => '',
                'analyticsEnabled' => (bool) get_option('q_pwa_analytics_enabled', true),
                'iconUrl' => $icon_url,
                'splashUrl' => $splash_url,
                'splashDuration' => (int) get_option('q_pwa_splash_duration', 2000),
                'appVersion' => self::get_plugin_version(),
                'currentUserId' => get_current_user_id(), // Add WordPress user ID for analytics tracking
                'installableOnDesktop' => (bool) get_option('q_pwa_installable_on_desktop', false),
                // Periodic sync settings
                'syncEnabled' => (bool) get_option('q_pwa_sync_enabled', true),
                'syncInterval' => (int) get_option('q_pwa_sync_interval', 3600000), // Default: 1 hour
                'syncAutoReload' => (bool) get_option('q_pwa_sync_auto_reload', false)
            ]);

            return;
        }

        // Admin scripts
        if ($hook === 'settings_page_q-pwa-settings') {
            // Ensure WordPress media library scripts are available for wp.media usage.
            // This must be loaded before our custom admin script so that wp.media is defined.
            if (function_exists('wp_enqueue_media')) {
                wp_enqueue_media();
            }

            // Enqueue analytics scripts and styles if on analytics tab
            if (isset($_GET['tab']) && $_GET['tab'] === 'analytics') {

                // Analytics JS
                wp_enqueue_script(
                    'q-pwa-analytics',
                    plugins_url('includes/js/pwa-analytics.js', dirname(__FILE__)),
                    array('jquery'),
                    filemtime(Q_PLUGIN_DIR . 'includes/js/pwa-analytics.js'),
                    true
                );

                // Pass analytics data to JS
                wp_localize_script('q-pwa-analytics', 'qPWAAnalytics', [
                    'ajaxUrl' => admin_url('admin-ajax.php'),
                    'nonce' => wp_create_nonce('q_pwa_analytics_nonce'),
                    'refreshInterval' => get_option('q_pwa_analytics_refresh_interval', 30) // seconds
                ]);
            }
        }

        // Only load admin scripts on PWA settings page
        if ($hook !== 'settings_page_q-pwa-settings') {
            return;
        }

        // Verify script dependencies are registered
        if (!wp_script_is('jquery', 'registered')) {
            error_log('PWA Admin: jQuery dependency missing');
            return;
        }

        // Enqueue admin CSS with version based on filemtime
        $css_path = Q_PLUGIN_DIR . 'includes/css/pwa-admin.css';
        wp_enqueue_style(
            'q-pwa-admin-styles',
            plugins_url('includes/css/pwa-admin.css', dirname(__FILE__)),
            array(),
            filemtime($css_path)
        );

        // Enqueue admin JavaScript with version based on filemtime
        $js_path = Q_PLUGIN_DIR . 'includes/js/pwa-admin.js';
        wp_enqueue_script(
            'q-pwa-admin',
            plugins_url('includes/js/pwa-admin.js', dirname(__FILE__)),
            array('jquery', 'media-editor'),
            filemtime($js_path),
            true
        );

        // Enqueue splash screen preview script
        $splash_preview_path = Q_PLUGIN_DIR . 'includes/js/splash-screen-preview.js';
        wp_enqueue_script(
            'q-pwa-splash-preview',
            plugins_url('includes/js/splash-screen-preview.js', dirname(__FILE__)),
            array('jquery'),
            file_exists($splash_preview_path) ? filemtime($splash_preview_path) : '1.0',
            true
        );

        // Pass data to JavaScript
        wp_localize_script('q-pwa-admin', 'qPWAAdmin', [
            'manifestUrl' => Q_PWA_Manifest::get_manifest_url(),
            'siteUrl' => home_url('/'),
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('q_pwa_admin_nonce'),
            'isFirstTime' => !get_option('q_pwa_enabled', false)
        ]);

        // Pass PWA settings to JavaScript for preview functionality
        wp_localize_script('q-pwa-splash-preview', 'qPWASettings', [
            'iconUrl' => get_option('q_pwa_icon', ''),
            'splashUrl' => get_option('q_pwa_splash', ''),
            'backgroundColor' => get_option('q_pwa_background_color', '#ffffff'),
            'themeColor' => get_option('q_pwa_theme_color', '#ffffff'),
            'appName' => get_option('q_pwa_app_name', get_bloginfo('name')),
            'appShortName' => get_option('q_pwa_app_short_name', get_bloginfo('name')),
            'splashDuration' => (int)get_option('q_pwa_splash_duration', 2000)
        ]);
    }

    /**
     * Sanitize iOS status bar style
     */
    public static function sanitize_ios_status_bar_style($value)
    {
        $valid_styles = ['default', 'black', 'black-translucent'];
        return in_array($value, $valid_styles) ? $value : 'default';
    }

    /**
     * Get plugin version
     */
    public static function get_plugin_version()
    {
        // Try to get version from plugin data
        if (!function_exists('get_plugin_data')) {
            require_once(ABSPATH . 'wp-admin/includes/plugin.php');
        }

        $plugin_file = Q_PLUGIN_DIR . 'q-pusher.php';
        if (file_exists($plugin_file)) {
            $plugin_data = get_plugin_data($plugin_file);
            return $plugin_data['Version'] ?? '2.2.2';
        }

        return '2.2.2'; // Fallback version
    }

    /**
     * Check if an updated option is a PWA setting and flush manifest cache if needed
     */
    public static function maybe_flush_manifest_cache($option_name, $old_value, $new_value)
    {
        // Check if this is a PWA setting
        if (strpos($option_name, 'q_pwa_') === 0) {
            // Only flush if the value actually changed
            if ($old_value !== $new_value) {
                error_log('PWA Setting updated: ' . $option_name . ' - Flushing manifest cache');

                // Update a timestamp option to force manifest regeneration
                update_option('q_pwa_manifest_updated', time());

                if (class_exists('Q_PWA_Manifest')) {
                    Q_PWA_Manifest::flush_manifest_cache();
                }
                // If the caching strategy or cache version was changed, regenerate the service worker
                if ($option_name === 'q_pwa_caching_strategy' || $option_name === 'q_pwa_cache_version') {
                    if (function_exists('q_copy_service_worker')) {
                        q_copy_service_worker();
                    }
                }
            }
        }
    }

    public static function remove_site_icon_meta()
    {
        // Only proceed if the PWA is enabled
        if (!get_option('q_pwa_enabled', false)) {
            return;
        }

        // Check if a dedicated PWA icon has been configured
        $pwa_icon = get_option('q_pwa_icon', '');
        if (empty($pwa_icon)) {
            // If there is no explicit PWA icon we keep WordPress' default site icon
            return;
        }

        // Remove the default WordPress site icon meta so the PWA icon takes precedence
        remove_action('wp_head', 'wp_site_icon', 99);
    }

    // Add a render_radio_field method if not present
    public static function render_radio_field($args)
    {
        $field = $args['field'];
        $value = get_option($field, '0');
        $options = $args['options'] ?? [1 => 'Yes', 0 => 'No'];
        $description = $args['description'] ?? '';
        foreach ($options as $opt_value => $label) {
            echo '<label style="margin-right:15px;">';
            echo '<input type="radio" name="' . esc_attr($field) . '" value="' . esc_attr($opt_value) . '"' . checked($value, $opt_value, false) . '> ' . esc_html($label);
            echo '</label>';
        }
        if ($description) {
            echo '<p class="description">' . esc_html($description) . '</p>';
        }
    }
}
