importScripts(
  "https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js"
);
importScripts(
  "https://www.gstatic.com/firebasejs/9.23.0/firebase-messaging-compat.js"
);

console.log("Minimal Service Worker script loaded.");

const Q_PWA_CACHE_NAME = "q-pwa-cache-v2";
const SYNC_TAG = "q-pwa-periodic-sync";

// Default offline URL - will be replaced by WordPress during installation
const Q_PWA_OFFLINE_URL = "/offline/";
const Q_PWA_CACHING_STRATEGY = "network-first";

// Initialize Firebase with configuration details and error handling
try {
  firebase.initializeApp({
    apiKey: "AIzaSyDQoFn5Xg3JOgTG1BHYiJDJOCyJzX6Zx-Q",
    authDomain: "q-push-50a80.firebaseapp.com",
    projectId: "q-push-50a80",
    storageBucket: "q-push-50a80.appspot.com",
    messagingSenderId: "958555222608",
    appId: "1:958555222608:web:3a9a9a9a9a9a9a9a9a9a9a",
  });
  const messaging = firebase.messaging();

  // Enhanced notification tracking with better validation
  const notificationCache = {
    messages: new Map(),
    cleanupInterval: 300000, // 5 minutes
    maxAge: 300000, // 5 minutes

    addMessage(key, timestamp) {
      if (!key) return false;
      this.cleanup();
      this.messages.set(key, timestamp);
      return true;
    },

    hasMessage(key) {
      if (!key) return false;
      this.cleanup();
      return this.messages.has(key);
    },

    cleanup() {
      const now = Date.now();
      let cleaned = 0;
      for (const [key, timestamp] of this.messages.entries()) {
        if (now - timestamp > this.maxAge) {
          this.messages.delete(key);
          cleaned++;
        }
      }
      if (self.location.hostname === "localhost") {
        console.log(`Cleaned ${cleaned} old notifications`);
      }
    },
  };

  // Handle background messages with improved error handling and prevent duplicate notifications
  messaging.onBackgroundMessage(async (payload) => {
    try {
      if (self.location.hostname === "localhost") {
        console.log("Processing background message:", payload);
      }

      // Handle data-only messages
      const { data, notification } = payload;

      // Use notification object if available, otherwise use data
      const title = notification?.title || data?.title || "New Notification";
      const body = notification?.body || data?.body || "";

      // Generate a unique notification key using multiple properties
      const notificationKey = [
        data?.message_id,
        data?.notification_id,
        title,
        data?.timestamp || Date.now(),
        data?.form_id,
      ]
        .filter(Boolean)
        .join("-");

      if (notificationCache.hasMessage(notificationKey)) {
        if (self.location.hostname === "localhost") {
          console.log("Duplicate notification filtered:", notificationKey);
        }
        return;
      }

      const timestamp = parseInt(data?.timestamp || Date.now() / 1000) * 1000;
      const now = Date.now();

      // Increase max age to 5 minutes to prevent discarding valid notifications
      if (now - timestamp > 5 * 60 * 1000) {
        if (self.location.hostname === "localhost") {
          console.log("Discarding outdated notification:", notificationKey);
        }
        return;
      }

      notificationCache.addMessage(notificationKey, now);

      // Create and show the notification
      const notificationOptions = {
        title: title,
        body: body,
        data: {
          url: data?.click_action || self.registration.scope,
          notification_id: data?.notification_id || "",
          form_id: data?.form_id || "",
          message_id: data?.message_id || notificationKey,
          // Include all data properties for access in notification handlers
          ...data,
        },
        requireInteraction: true,
        tag: notificationKey, // Use the same key for the tag to prevent duplicates
        click_action: data?.click_action || self.registration.scope,
        silent: false, // Ensure sound is played
      };

      // Only add icon if explicitly provided
      if (data.icon) {
        notificationOptions.icon = data.icon;
        notificationOptions.badge = data.badge || data.icon;
      }

      // Only add image if it exists and is a valid URL
      if (data.image && data.image.startsWith("http")) {
        notificationOptions.image = data.image;
      }

      // Process rich media options
      if (data.style) {
        switch (data.style) {
          case "big-picture":
            // Use large_image if available, otherwise fall back to image
            if (data.large_image) {
              notificationOptions.image = data.large_image;
            }
            break;

          case "carousel":
            // Add carousel images if supported
            if (data.carousel_images) {
              try {
                const carouselImages = JSON.parse(data.carousel_images);
                if (
                  Array.isArray(carouselImages) &&
                  carouselImages.length > 0
                ) {
                  // Store images for client-side rendering
                  notificationOptions.data.carouselImages = carouselImages;
                  // Use first image as preview
                  notificationOptions.image = carouselImages[0];
                }
              } catch (e) {
                console.error("Failed to parse carousel images:", e);
              }
            }
            break;

          case "progress":
            // Add progress information
            if (data.progress_current && data.progress_max) {
              notificationOptions.data.progress = {
                current: parseInt(data.progress_current),
                max: parseInt(data.progress_max),
                indeterminate: data.progress_indeterminate === "true",
              };
            }
            break;

          case "map":
            // Add map location data
            if (data.map_lat && data.map_lng) {
              notificationOptions.data.mapLocation = {
                lat: parseFloat(data.map_lat),
                lng: parseFloat(data.map_lng),
                zoom: parseInt(data.map_zoom || 14),
              };
            }
            break;
        }
      }

      // Add video thumbnail if available
      if (data.video_thumbnail) {
        notificationOptions.image = data.video_thumbnail;
        notificationOptions.data.videoUrl = data.video_url || "";
      }

      // Show the notification - works regardless of online/offline status
      if (self.location.hostname === "localhost") {
        console.log(
          "Attempting to show notification:",
          title,
          notificationOptions
        );
        console.log(
          "Network status: ",
          navigator.onLine ? "online" : "offline"
        );
      }
      await self.registration.showNotification(title, notificationOptions);
      if (self.location.hostname === "localhost") {
        console.log(
          "Notification shown successfully (offline capable):",
          title
        );
      }
    } catch (err) {
      console.error(
        "Service Worker: Error showing notification or in onBackgroundMessage",
        err
      );
      // Notifications should still work offline, so this is likely a different issue
      if (self.location.hostname === "localhost") {
        console.error("Notification error details:", {
          online: navigator.onLine,
          title: title,
          error: err.message,
        });
      }
    }
  });
} catch (e) {
  console.error("Service Worker: Firebase initialization failed", e);
}

self.addEventListener("install", function (event) {
  console.log("Service Worker installing.");
  event.waitUntil(
    (async () => {
      const cache = await caches.open(Q_PWA_CACHE_NAME);
      try {
        // First, cache the offline page - this is critical
        await cache.add(Q_PWA_OFFLINE_URL);

        // Then cache common assets
        await cache.addAll([
          // Core WordPress assets
          "/wp-includes/css/dist/block-library/style.min.css",
          "/wp-includes/js/jquery/jquery.min.js",
          "/wp-includes/js/jquery/jquery-migrate.min.js",

          // Theme assets - adjust based on your theme
          "/wp-content/themes/hello-elementor/style.css",
          "/wp-content/themes/hello-elementor/assets/css/theme.css",
          "/wp-content/themes/hello-elementor/assets/css/reset.css",
          "/wp-content/themes/hello-elementor/assets/css/header-footer.css",

          // Plugin CSS
          "/wp-content/plugins/q-pusher-q-pwa/includes/css/pwa-styles.css",
          "/wp-content/plugins/q-pusher-q-pwa/includes/css/offline.css",

          // Common fonts
          "https://fonts.googleapis.com/css?family=Roboto:400,500,700&display=swap",

          // Homepage (for offline access to main content)
          "/",
        ]);

        console.log(
          "Service Worker: Successfully cached offline page and assets"
        );
      } catch (error) {
        console.error("Service Worker: Failed to cache offline assets:", error);
      }
    })()
  );
});

self.addEventListener("activate", (event) => {
  console.log("Service Worker activating.");
  // Clean up old caches and remove any cached admin pages
  event.waitUntil(
    Promise.all([
      // Clean up old cache versions
      caches.keys().then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (
              cacheName.startsWith("q-pwa-") &&
              cacheName !== Q_PWA_CACHE_NAME
            ) {
              console.log("Service Worker deleting old cache:", cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      }),
      // Remove any cached admin pages from current cache
      caches.open(Q_PWA_CACHE_NAME).then(async (cache) => {
        const cachedRequests = await cache.keys();
        const adminRequests = cachedRequests.filter((request) => {
          const url = new URL(request.url);
          return (
            url.pathname.includes("/wp-admin/") ||
            url.pathname.includes("/wp-login.php") ||
            url.pathname.includes("/wp-register.php") ||
            url.pathname.includes("/wp-cron.php") ||
            url.pathname.includes("/xmlrpc.php") ||
            url.searchParams.has("preview") ||
            url.searchParams.has("customize_changeset_uuid") ||
            url.pathname.includes("/wp-json/")
          );
        });

        if (adminRequests.length > 0) {
          console.log(
            `Service Worker: Removing ${adminRequests.length} cached admin pages`
          );
          return Promise.all(
            adminRequests.map((request) => cache.delete(request))
          );
        }
      }),
    ])
  );

  // Immediately claim clients so the service worker can control open pages
  event.waitUntil(self.clients.claim());
});

// Handle periodic background sync
self.addEventListener("periodicsync", (event) => {
  if (event.tag === SYNC_TAG) {
    event.waitUntil(handlePeriodicSync());
  }
});

async function handlePeriodicSync() {
  try {
    console.log(
      "Periodic background sync started at:",
      new Date().toISOString()
    );

    // Get all clients (windows/tabs) controlled by this service worker
    const clients = await self.clients.matchAll();

    // Send sync event to all clients
    clients.forEach((client) => {
      client.postMessage({
        type: "PERIODIC_SYNC",
        timestamp: Date.now(),
      });
    });

    // Check for cached content that needs updating
    const cache = await caches.open(Q_PWA_CACHE_NAME);
    const cachedRequests = await cache.keys();

    // Track sync statistics
    const syncStats = {
      total: cachedRequests.length,
      updated: 0,
      failed: 0,
      unchanged: 0,
    };

    // Prioritize HTML pages first, then other resources
    const htmlRequests = cachedRequests.filter((req) => {
      const url = new URL(req.url);
      return !url.pathname.match(
        /\.(js|css|png|jpg|jpeg|gif|svg|woff|woff2|ttf|eot)$/i
      );
    });

    const assetRequests = cachedRequests.filter((req) => {
      const url = new URL(req.url);
      return url.pathname.match(
        /\.(js|css|png|jpg|jpeg|gif|svg|woff|woff2|ttf|eot)$/i
      );
    });

    // Process HTML pages first (they're more important for user experience)
    console.log(
      `Updating ${htmlRequests.length} HTML pages and ${assetRequests.length} assets`
    );

    // Update HTML pages
    for (const request of htmlRequests) {
      try {
        // Skip the offline page - we don't want to update it from network
        if (request.url.includes("/offline/")) {
          syncStats.unchanged++;
          continue;
        }

        // Skip admin URLs during periodic sync
        const url = new URL(request.url);
        const isAdminRequest =
          url.pathname.includes("/wp-admin/") ||
          url.pathname.includes("/wp-login.php") ||
          url.pathname.includes("/wp-register.php") ||
          url.pathname.includes("/wp-cron.php") ||
          url.pathname.includes("/xmlrpc.php") ||
          url.searchParams.has("preview") ||
          url.searchParams.has("customize_changeset_uuid") ||
          url.pathname.includes("/wp-json/");

        if (isAdminRequest) {
          syncStats.unchanged++;
          continue;
        }

        // Add cache-busting parameter to avoid getting cached responses
        const bustUrl = new URL(request.url);
        bustUrl.searchParams.set("_pwa_sync", Date.now());

        const response = await fetch(bustUrl, {
          cache: "no-cache",
          headers: {
            "X-PWA-Sync": "true",
          },
        });

        if (response && response.ok) {
          // Store the original URL in the cache, not the one with cache-busting
          await cache.put(request, response);
          syncStats.updated++;
        } else {
          syncStats.failed++;
        }
      } catch (err) {
        console.error("Error updating cached HTML:", request.url, err);
        syncStats.failed++;
      }
    }

    // Update assets (with a slight delay to prioritize HTML content)
    for (const request of assetRequests) {
      try {
        const response = await fetch(request, { cache: "no-cache" });
        if (response && response.ok) {
          await cache.put(request, response);
          syncStats.updated++;
        } else {
          syncStats.failed++;
        }
      } catch (err) {
        console.error("Error updating cached asset:", request.url, err);
        syncStats.failed++;
      }
    }

    console.log("Periodic sync completed:", syncStats);

    // Notify clients about the sync completion
    clients.forEach((client) => {
      client.postMessage({
        type: "PERIODIC_SYNC_COMPLETED",
        timestamp: Date.now(),
        stats: syncStats,
      });
    });
  } catch (err) {
    console.error("Error in periodic sync:", err);
  }
}

// Handle push and notification events
self.addEventListener("push", (event) => {
  if (!event.data) return;
  let data = {};
  try {
    data = event.data.json();
  } catch (err) {
    return;
  }
  if (data && data.type === "pwa-update") {
    event.waitUntil(handlePWAUpdate(data));
  }
});

function handlePWAUpdate(data) {
  if (self.location.hostname === "localhost") {
    console.log("Service Worker: PWA update available", data);
  }
  self.clients.matchAll().then((clients) => {
    clients.forEach((client) => {
      client.postMessage({
        type: "PWA_UPDATE_AVAILABLE",
        data: data,
      });
    });
  });
}

// Add notification click event handler
self.addEventListener("notificationclick", function (event) {
  event.notification.close();

  // Track notification click
  const notificationData = event.notification.data || {};
  const trackingData = {
    notification_id: notificationData.notification_id || "",
    form_id: notificationData.form_id || "",
    sequence_id: notificationData.sequence_id || "",
  };

  // Send tracking data to the main thread
  event.waitUntil(
    self.clients
      .matchAll({ type: "window", includeUncontrolled: true })
      .then(function (clientList) {
        // Send tracking message to all clients
        clientList.forEach((client) => {
          client.postMessage({
            type: "NOTIFICATION_CLICKED",
            data: trackingData,
          });
        });

        // Handle URL navigation
        let url = notificationData.url || self.registration.scope;

        // If a window/tab is already open with the target URL, focus it; otherwise, open a new one
        for (let i = 0; i < clientList.length; i++) {
          let client = clientList[i];
          if (client.url === url && "focus" in client) {
            return client.focus();
          }
        }
        if (self.clients.openWindow) {
          return self.clients.openWindow(url);
        }
      })
  );
});

// --- OFFLINE & CACHING STRATEGY CONFIGURATION ---
// Note: These values are already defined at the top of the file and will be replaced by WordPress during installation

self.addEventListener("fetch", function (event) {
  const req = event.request;

  // Skip non-GET requests and requests to other domains
  if (req.method !== "GET" || !req.url.startsWith(self.location.origin)) {
    return;
  }

  // Handle static assets (CSS, JS, images, fonts)
  if (["style", "script", "image", "font"].includes(req.destination)) {
    event.respondWith(
      caches.match(req).then(function (response) {
        return (
          response ||
          fetch(req)
            .then(function (networkResponse) {
              // Cache successful responses
              if (networkResponse && networkResponse.ok) {
                const responseToCache = networkResponse.clone();
                caches.open(Q_PWA_CACHE_NAME).then(function (cache) {
                  cache.put(req, responseToCache);
                });
              }
              return networkResponse;
            })
            .catch(function () {
              // For images, return a fallback if available
              if (req.destination === "image") {
                return caches
                  .match(
                    "/wp-content/plugins/q-pusher-q-pwa/includes/images/offline-image.png"
                  )
                  .then((response) => {
                    if (response) {
                      return response;
                    }
                    // If offline image doesn't exist, create a simple SVG
                    return new Response(
                      '<svg xmlns="http://www.w3.org/2000/svg" width="400" height="300" viewBox="0 0 400 300" fill="none"><rect width="400" height="300" fill="#f1f5f9"/><text x="50%" y="50%" font-family="system-ui, sans-serif" font-size="24" text-anchor="middle" fill="#64748b">Image unavailable offline</text></svg>',
                      {
                        status: 200,
                        headers: { "Content-Type": "image/svg+xml" },
                      }
                    );
                  });
              }
              return new Response("Resource unavailable offline", {
                status: 408,
              });
            })
        );
      })
    );
    return;
  }

  // Skip caching for WordPress admin area and other admin-related URLs
  const url = new URL(req.url);
  const isAdminRequest =
    url.pathname.includes("/wp-admin/") ||
    url.pathname.includes("/wp-login.php") ||
    url.pathname.includes("/wp-register.php") ||
    url.pathname.includes("/wp-cron.php") ||
    url.pathname.includes("/xmlrpc.php") ||
    url.searchParams.has("preview") ||
    url.searchParams.has("customize_changeset_uuid") ||
    url.pathname.includes("/wp-json/") ||
    req.headers.get("X-Requested-With") === "XMLHttpRequest";

  // Handle navigation requests (HTML pages) - but exclude admin areas
  if (
    !isAdminRequest &&
    (req.mode === "navigate" ||
      (req.method === "GET" &&
        req.headers.get("accept") &&
        req.headers.get("accept").includes("text/html")))
  ) {
    event.respondWith(
      (async () => {
        const cache = await caches.open(Q_PWA_CACHE_NAME);
        const cachingStrategy = Q_PWA_CACHING_STRATEGY;
        const offlineUrl = Q_PWA_OFFLINE_URL;

        // Try to get a cached response for this specific URL
        const cachedResponse = await cache.match(req);

        try {
          // Different strategies based on configuration
          if (cachingStrategy === "cache-first") {
            // Cache-first: return cached response if available, otherwise try network
            if (cachedResponse) {
              return cachedResponse;
            }

            try {
              const networkResponse = await fetch(req);
              if (networkResponse && networkResponse.ok) {
                // Cache the successful response for future use
                cache.put(req, networkResponse.clone());
              }
              return networkResponse;
            } catch (error) {
              // Network failed, serve offline page
              const offlineResponse = await cache.match(offlineUrl);
              if (offlineResponse) {
                return offlineResponse;
              }
              // If offline page isn't cached, throw the original error
              throw error;
            }
          } else if (cachingStrategy === "stale-while-revalidate") {
            // Stale-while-revalidate: return cached response immediately, update cache in background
            const fetchPromise = fetch(req)
              .then((networkResponse) => {
                if (networkResponse && networkResponse.ok) {
                  cache.put(req, networkResponse.clone());
                }
                return networkResponse;
              })
              .catch(() => null);

            // If we have a cached version, use it while we update the cache
            if (cachedResponse) {
              fetchPromise; // Don't await, update cache in background
              return cachedResponse;
            }

            // No cached version, wait for the network response
            const networkResult = await fetchPromise;
            if (networkResult) {
              return networkResult;
            }

            // Network failed and no cache, serve offline page
            const offlineResponse = await cache.match(offlineUrl);
            if (offlineResponse) {
              return offlineResponse;
            }

            // Everything failed
            return new Response("Service unavailable", { status: 503 });
          } else {
            // Network-first (default): try network first, fall back to cache
            try {
              const networkResponse = await fetch(req);
              if (networkResponse && networkResponse.ok) {
                // Cache the successful response
                cache.put(req, networkResponse.clone());
                return networkResponse;
              }
              // Network response wasn't ok, try cache
              if (cachedResponse) {
                return cachedResponse;
              }
              // No cache, serve offline page
              const offlineResponse = await cache.match(offlineUrl);
              if (offlineResponse) {
                return offlineResponse;
              }
              // Return the not-ok network response as last resort
              return networkResponse;
            } catch (error) {
              // Network error, try cache
              if (cachedResponse) {
                return cachedResponse;
              }
              // No cache, serve offline page
              const offlineResponse = await cache.match(offlineUrl);
              if (offlineResponse) {
                return offlineResponse;
              }
              // Everything failed
              throw error;
            }
          }
        } catch (error) {
          console.error("Service Worker fetch handler error:", error);
          // Final fallback - try to return the offline page
          try {
            const offlineResponse = await cache.match(offlineUrl);
            if (offlineResponse) {
              return offlineResponse;
            }
          } catch (e) {
            // Even the offline page failed
            console.error("Failed to serve offline page:", e);
          }

          // Create a basic offline response as last resort
          return new Response(
            "<html><head><title>Offline</title></head><body><h1>You are offline</h1><p>Please check your internet connection.</p></body></html>",
            {
              status: 503,
              headers: { "Content-Type": "text/html" },
            }
          );
        }
      })()
    );
  }
});
